version: '3.8'

services:
  postgres:
    image: bitnami/postgresql:latest
    ports:
      - "${POSTGRESSQL_PORT:-5432}:5432"
    environment:
      - POSTGRESQL_USERNAME=${POSTGRESQL_USERNAME:-postgres}
      - POSTGRESQL_PASSWORD=${POSTGRESQL_PASSWORD:-postgres}
      - POSTGRESQL_DATABASE=${POSTGRESQL_DATABASE:-postgres}
    volumes:
      - postgres_data:/bitnami/postgresql
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
