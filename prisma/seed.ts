import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Get credentials from environment variables
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
  const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

  const subAdminEmail =
    process.env.DEFAULT_SUBADMIN_EMAIL || '<EMAIL>';
  const subAdminPassword =
    process.env.DEFAULT_SUBADMIN_PASSWORD || 'subadmin123';
  const subAdminName = process.env.DEFAULT_SUBADMIN_NAME || 'Content Manager';

  // Create admin user
  const hashedAdminPassword = await bcrypt.hash(adminPassword, 12);

  const admin = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      name: adminN<PERSON>,
      email: adminEmail,
      password: hashedAdminPassword,
      role: 'ADMIN',
      bio: 'System administrator with full access to all features and content management.',
      isActive: true,
    },
  });

  // Create a sub-admin user
  const hashedSubAdminPassword = await bcrypt.hash(subAdminPassword, 12);

  const subAdmin = await prisma.user.upsert({
    where: { email: subAdminEmail },
    update: {},
    create: {
      name: subAdminName,
      email: subAdminEmail,
      password: hashedSubAdminPassword,
      role: 'SUB_ADMIN',
      bio: 'Content manager responsible for creating and managing posts across all sections.',
      isActive: true,
      createdById: admin.id,
    },
  });

  console.log('✅ Database seed completed successfully!');
  console.log(`📧 Admin login: ${adminEmail} / ${adminPassword}`);
  console.log(`📧 Sub-admin login: ${subAdminEmail} / ${subAdminPassword}`);
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
