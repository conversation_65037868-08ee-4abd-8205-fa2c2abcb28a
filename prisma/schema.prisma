// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  ADMIN
  SUB_ADMIN
  GUEST
}

enum PostType {
  ANNOUNCEMENT
  GALLERY
  HALL_OF_FAME
}

model User {
  id        String   @id @default(cuid())
  name      String?
  email     String   @unique
  password  String
  role      Role     @default(GUEST)
  image     String?
  bio       String?
  joinedAt  DateTime @default(now())
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts        Post[]
  createdUsers User[]    @relation("UserCreator")
  createdBy    User?     @relation("UserCreator", fields: [createdById], references: [id])
  createdById  String?
  account      Account[]
  session      Session[]

  @@map("users")
}

model Post {
  id        String   @id @default(cuid())
  title     String
  content   String
  type      PostType @default(ANNOUNCEMENT)
  published <PERSON><PERSON>an  @default(true)
  imageUrl  String?
  tags      String[] @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  author   User   @relation(fields: [authorId], references: [id])
  authorId String

  @@map("posts")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}
